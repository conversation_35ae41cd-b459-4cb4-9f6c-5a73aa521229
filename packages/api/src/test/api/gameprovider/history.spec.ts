import { application } from "../../../skywind/serverGameAuth";
import { truncate } from "../../entities/helper";
import { generateGameToken } from "../../../skywind/utils/token";
import { BaseEntity } from "../../../skywind/entities/entity";
import config from "../../../skywind/config";
import { publicId } from "@skywind-group/sw-utils";
import { getRoundHistoryModel } from "../../../skywind/models/roundHistory";
import { get as getAggrRoundHistoryModel } from "../../../skywind/models/aggrround";
import { getGameInitSettingsModel } from "../../../skywind/models/gameInitSettings";
import { getSpinHistoryModel } from "../../../skywind/models/spinHistory";
import { FACTORY } from "../../factories/common";
import { DEFAULT_LIMIT } from "../../../skywind/history/decorators";

const request = require("supertest");

const chai = require("chai");
const FactoryGirl = require("factory-girl");
chai.use(require("chai-shallow-deep-equal"));

const expect = chai.expect;

describe("Provider GameHistory", function () {
    this.timeout(20000);
    let server;
    let gameToken: string;
    let brand: BaseEntity;
    const factory = FactoryGirl.factory;
    const playerCode = "somePlayer";
    const gameCode = "gameCode1";

    const getGameProviderUrl = (path: string = "") => {
        return `/v1/history${path}`;
    };

    const isPublicId = (pId: string): boolean => {
        if (!pId) {
            return false;
        }

        try {
            const decodedId = publicId.instance.decode(pId);
            return Number.isFinite(decodedId);
        } catch (err) {
            return false;
        }
    };

    before(async () => {
        const app = await application.get();
        await app.ready();
        server = app.server;

        await truncate();

        await getGameInitSettingsModel().drop();
        await getGameInitSettingsModel().sync();

        await getRoundHistoryModel().drop({ cascade: true });
        await getRoundHistoryModel().sync({ schema: config.db.schema });

        await getAggrRoundHistoryModel().drop({ cascade: true });
        await getAggrRoundHistoryModel().sync({ schema: config.db.schema });

        await getSpinHistoryModel().drop({ cascade: true});
        await getSpinHistoryModel().sync({ schema: config.db.schema });

        await factory.create(FACTORY.GAME, {}, { code: gameCode });

        brand = await factory.create(FACTORY.BRAND);

        gameToken = await generateGameToken({
            playerCode: playerCode,
            gameCode: gameCode,
            brandId: brand.id,
            currency: "USD"
        });

        await factory.createMany(FACTORY.AGGR_ROUNDS, 10, {}, {
            brandId: brand.id,
            gameCode,
            playerCode
        });

        const rounds = await factory.createMany(FACTORY.ROUND_HISTORY, 10, {}, {
            brandId: brand.id,
            gameCode,
            playerCode
        });

        for (const round of rounds) {
            await factory.createMany(FACTORY.SPIN_HISTORY, 10, {}, {
                roundId: round.roundId,
                brandId: brand.id,
                playerCode,
                gameCode
            });
        }
    });

    describe("/rounds", () => {

        it("Get new rounds", async () => {
            config.gameHistory.newRounds = true;

            await request(server)
                .get(getGameProviderUrl("/rounds"))
                .query({ gameToken: gameToken })
                .then((res) => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body).that.is.a("array");
                    expect(res.body.length).to.be.equal(10);
                    res.body.forEach(round => {
                        expect(isPublicId(round.roundId)).to.be.true;
                        expect(round.playerCode).to.be.equal(playerCode);
                    });

                });
        });

        it("Get old rounds", async () => {
            config.gameHistory.newRounds = false;

            await request(server)
                .get(getGameProviderUrl("/rounds"))
                .query({ gameToken: gameToken })
                .then((res) => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body).that.is.a("array");
                    expect(res.body.length).to.be.equal(10);
                    res.body.forEach(round => {
                        expect(isPublicId(round.roundId)).to.be.true;
                        expect(round.playerCode).to.be.equal(playerCode);
                    });
                });
        });
    });

    describe("/rounds/:roundId", () => {
        it("Get event history", async () => {

            const roundId = publicId.instance.encode(2);
            await request(server)
                .get(getGameProviderUrl(`/rounds/${roundId}`))
                .query({ gameToken: gameToken })
                .then((res) => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body).that.is.a("array");
                    expect(res.body.length).to.be.equal(10);
                    res.body.forEach(spin => {
                        expect(Number.isFinite(spin.spinNumber)).to.be.true;
                    });

                    expect(res.headers["x-paging-total"]).to.be.equal("10");
                    expect(res.headers["x-paging-limit"]).to.be.exist;
                    expect(res.headers["x-paging-offset"]).to.be.exist;
                });
        });

        it("Get round info - fail", async () => {
            const roundId = 2;
            await request(server)
                .get(getGameProviderUrl(`/rounds/${roundId}`))
                .query({ gameToken: gameToken })
                .then(res => {
                    expect(res.status).to.be.equal(400);
                    expect(res.body.message).to.be.equal("Validation error: roundId - invalid public id value");
                });
        });
    });

    describe("/rounds/:roundId/events/:eventId", () => {

        it("Get event detail info", async () => {

            const roundId = publicId.instance.encode(1);
            const eventId = publicId.instance.encode(10);
            await request(server)
                .get(getGameProviderUrl(`/rounds/${roundId}/events/${eventId}`))
                .query({ gameToken: gameToken })
                .then((res) => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body.roundId).to.equal(roundId);
                    expect(res.body.spinNumber).to.equal(10);
                });
        });

        it("Get event detail info with non-public roundId and eventId", async () => {

            const roundId = publicId.instance.encode(1);
            await request(server)
                .get(getGameProviderUrl("/rounds/1/events/10"))
                .query({ gameToken: gameToken })
                .then(res => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body.roundId).to.equal(roundId);
                    expect(res.body.spinNumber).to.equal(10);
                });
        });

        it("Get event detail info with non-public eventId", async () => {
            const roundId = publicId.instance.encode(1);
            await request(server)
                .get(getGameProviderUrl(`/rounds/${roundId}/events/10`))
                .query({ gameToken: gameToken })
                .then(res => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body.roundId).to.equal(roundId);
                    expect(res.body.spinNumber).to.equal(10);
                });
        });

        it("Get event detail info with wrong spinNumber", async () => {
            const roundId = publicId.instance.encode(1);
            await request(server)
                .get(getGameProviderUrl(`/rounds/${roundId}/events/awdawdawd`))
                .query({ gameToken: gameToken })
                .then(res => {
                    expect(res.status).to.be.equal(400);
                });
        });
    });

    describe("/events", () => {

        it("get events", async () => {

            await request(server)
                .get(getGameProviderUrl("/events"))
                .query({ gameToken: gameToken })
                .then((res) => {
                    expect(res.status).to.be.equal(200);
                    expect(res.body).that.is.a("array");
                    expect(res.body.length).to.be.equal(DEFAULT_LIMIT);
                    res.body.forEach(round => {
                        expect(isPublicId(round.roundId)).to.be.true;
                        expect(Number.isFinite(round.spinNumber)).to.be.true;
                    });

                });
        });
    });

    describe("gameVersion", () => {
        it ("check game version", async () => {
            await request(server)
                .get(getGameProviderUrl("/gameVersion"))
                .query({ gameToken, gameVersion: "1.0.0" })
                .then((res) => {
                    expect(res.status).to.be.equal(404);
                });
        })
    })
});
